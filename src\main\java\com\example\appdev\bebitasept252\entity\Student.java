package com.example.appdev.bebitasept251.entity;

import jakarta.persistence.*;

@Entity
@Table(name = "student")
public class Student {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "firstname", nullable = false)
    private String firstname;
    
    @Column(name = "lastname", nullable = false)
    private String lastname;
    
    @Column(name = "program")
    private String program;
    
    @Column(name = "yearlevel")
    private Integer yearlevel;

    @Column(name = "email")
    private String email;

    // Default constructor
    public Student() {}

    // Constructor with parameters
    public Student(String firstname, String lastname, String program, Integer yearlevel) {
        this.firstname = firstname;
        this.lastname = lastname;
        this.program = program;
        this.yearlevel = yearlevel;
    }

    // Constructor with email
    public Student(String firstname, String lastname, String program, Integer yearlevel, String email) {
        this.firstname = firstname;
        this.lastname = lastname;
        this.program = program;
        this.yearlevel = yearlevel;
        this.email = email;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getFirstname() {
        return firstname;
    }
    
    public void setFirstname(String firstname) {
        this.firstname = firstname;
    }
    
    public String getLastname() {
        return lastname;
    }
    
    public void setLastname(String lastname) {
        this.lastname = lastname;
    }
    
    public String getProgram() {
        return program;
    }
    
    public void setProgram(String program) {
        this.program = program;
    }
    
    public Integer getYearlevel() {
        return yearlevel;
    }
    
    public void setYearlevel(Integer yearlevel) {
        this.yearlevel = yearlevel;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Override
    public String toString() {
        return "Student{" +
                "id=" + id +
                ", firstname='" + firstname + '\'' +
                ", lastname='" + lastname + '\'' +
                ", program='" + program + '\'' +
                ", yearlevel=" + yearlevel +
                ", email='" + email + '\'' +
                '}';
    }
}
