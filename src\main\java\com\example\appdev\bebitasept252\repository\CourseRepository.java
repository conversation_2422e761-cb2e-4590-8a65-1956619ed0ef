package com.example.appdev.bebitasept251.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.example.appdev.bebitasept251.entity.Course;

import java.util.List;

@Repository
public interface CourseRepository extends JpaRepository<Course, Long> {
    
    // Custom query methods can be added here
    List<Course> findByUnits(Integer units);
    List<Course> findByDescriptionContainingIgnoreCase(String description);
}
