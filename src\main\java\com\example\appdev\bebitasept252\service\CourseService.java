package com.example.appdev.bebitasept252.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.example.appdev.bebitasept252.entity.Course;
import com.example.appdev.bebitasept252.repository.CourseRepository;

import java.util.List;
import java.util.Optional;

@Service
public class CourseService {
    
    @Autowired
    private CourseRepository courseRepository;
    
    // Get all courses
    public List<Course> getAllCourses() {
        return courseRepository.findAll();
    }
    
    // Get course by ID
    public Optional<Course> getCourseById(Long id) {
        return courseRepository.findById(id);
    }
    
    // Save course
    public Course saveCourse(Course course) {
        return courseRepository.save(course);
    }
    
    // Update course
    public Course updateCourse(Long id, Course courseDetails) {
        Optional<Course> optionalCourse = courseRepository.findById(id);
        if (optionalCourse.isPresent()) {
            Course course = optionalCourse.get();
            course.setDescription(courseDetails.getDescription());
            course.setUnits(courseDetails.getUnits());
            return courseRepository.save(course);
        }
        return null;
    }
    
    // Delete course
    public boolean deleteCourse(Long id) {
        if (courseRepository.existsById(id)) {
            courseRepository.deleteById(id);
            return true;
        }
        return false;
    }
    
    // Get courses by units
    public List<Course> getCoursesByUnits(Integer units) {
        return courseRepository.findByUnits(units);
    }
    
    // Search courses by description
    public List<Course> searchCoursesByDescription(String description) {
        return courseRepository.findByDescriptionContainingIgnoreCase(description);
    }
}
