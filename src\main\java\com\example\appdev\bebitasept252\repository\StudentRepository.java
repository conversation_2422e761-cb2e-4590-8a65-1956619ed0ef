package com.example.appdev.bebitasept251.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.example.appdev.bebitasept251.entity.Student;

import java.util.List;

@Repository
public interface StudentRepository extends JpaRepository<Student, Long> {
    
    // Custom query methods can be added here
    List<Student> findByProgram(String program);
    List<Student> findByYearlevel(Integer yearlevel);
    List<Student> findByFirstnameContainingIgnoreCase(String firstname);
    List<Student> findByLastnameContainingIgnoreCase(String lastname);
}
